def unlock_status(encoded_str: str):
    ascii_values = [int(s) for s in encoded_str.split('O') if s]
    decoded_str = ''.join(chr(i) for i in ascii_values)
    return decoded_str[::-1]


if __name__ == '__main__':
    print(unlock_status('48O124O48O124O55O53O46O52O54O46O54O57O46O50O56O49O124O48O124O48O124O48O124O48O124O111O97O105O108O105O115O121O114O111O109O101O109O124O50O52O53O49O50O50O49O50O49O50'))
    print(unlock_status('48O124O48O124O56O56O49O46O53O49O49O46O49O57O46O50O52O124O48O124O48O124O48O124O48O124O111O97O105O108O105O115O121O114O111O109O101O109O124O50O52O53O49O50O50O49O50O49O50'))