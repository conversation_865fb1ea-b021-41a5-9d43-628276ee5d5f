import requests
from urllib.parse import quote  # 登录时,把中文账号转码使用
from yzm import get_yzm
from proxy import get_proxy

def login(参_账号=None,参_密码=None):
    # 登录错误码
    _error_code = {
        '0': "登录失败,请重新登录",
        '1': "签名验证失败",
        '2': "时间戳过期",
        '3': "参数为空或格式不正确",
        '4': "用户名密码验证未通过",
        '5': "用户已被锁定",
        '6': "密保未通过",
        '7': "cookie验证未通过",
        '8': "token验证未通过",
        '9': "大区验证未通过",
        '11': "验证码错误",
        '12': "验证码为空",
        '999': "系统异常，登录失败"
    }
    # 登录信息
    while True:
        # 登录发送信息
        login_post = {
            'url': f'http://x12.pet.imop.com/LoginAction.jsp',
            'cookies': {'mopet_logon': '123'},
            'headers': {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Origin': f'http://x12.pet.imop.com',
                'Referer': f'http://x12.pet.imop.com/login.html',
                'Connection': 'keep-alive',
                'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
            },
            'data': {
                'user_name': 'dengjianabei',
                'password': '000000',
                'checkcode': get_yzm()
            },
        }
        # 登录返回信息
        login_response = requests.post(**login_post)
        if r'document.location="/pet.jsp"' in login_response.text:
            # print(login_response.text)
            cookies = login_response.cookies
            print('登陆成功')
            print(login_response.text)
            _get = {
                'cookies': cookies,
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://x12.pet.imop.com',
                        'Referer': f'http://x12.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
            }
            _get['url'] = f'http://x12.pet.imop.com/pet.jsp'
            join_response = requests.get(**_get)
            print('this\n' * 5,join_response.text)
        else:
            for _code in _error_code.keys():
                if f'errCode={_code}"' in login_response.text:
                    if _code == '11' or _code == '999':
                        # 错误码为11,验证码错误,重新获取验证码登录
                        # 错误码为999,封ip,重新获取验证码登录
                        continue
                    else:
                        print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                        return False

if __name__ == '__main__':
    login()
        