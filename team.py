from myj import MYJ
import time
from map import ret_way
import datetime
import json
from concurrent.futures import ThreadPoolExecutor,wait
import threading
import re

config = {
    "服务器": "s69",
    "账号": "yfydq1",
    "密码": "yfydq0101",
    "角色序号": "0",    
}

class MYJTeam:
    def __init__(self, configs):
        self.configs = configs
        self.myj_objects = [MYJ(config) for config in configs]
        self.线程池 = ThreadPoolExecutor(max_workers=len(self.myj_objects))
    
    def 单角色执行命令(self,s,index=0):
        self.myj_objects[index].send_orders(s)

    def 全体执行命令(self,s,delay=None):
        局_命令列表 = s.split('|')
        for 局_单条命令 in 局_命令列表:
            for myj in self.myj_objects:
                myj.send_orders(局_单条命令,0)
            if delay is not None:
                time.sleep(delay)
            else:
                time.sleep(self.myj_objects[0].延迟)

    def gto_地图房间(self, target):
        for obj in self.myj_objects:
            if obj.地图 is None:
                obj.refresh_info()
        futures = [self.线程池.submit(myj.gto, target) for myj in self.myj_objects]
        wait(futures)
        
    def gto_序号目标(self,index):
        for obj in self.myj_objects:
            if obj.地图 is None:
                obj.refresh_info()
        futures = [self.线程池.submit(myj.gto, self.myj_objects[index].地图 + '|' + self.myj_objects[index].房间) for myj in self.myj_objects]
        wait(futures)
            
    def 组队(self):
        for index in range(len(self.myj_objects)):
            if index == 0:
                pass
            else:
                self.myj_objects[0].send_orders(f'foo rank add {self.myj_objects[index].宠物名}',0)
                self.myj_objects[index].send_orders(f'foo rank agree {self.myj_objects[0].petId}')
                # self.myj_objects[index].send_orders(f'foo rank agree {self.myj_objects[0].petId}|follow none|follow {self.myj_objects[0].宠物名}')

    def 悬赏(self):
        局_筋斗云原始CD = {index: self.configs[index].get('筋斗云CD',0) for index in range(len(self.configs))}
        局_筋斗云剩余CD = {index: 0 for index in range(len(self.configs))}
        局_领取任务文本正则 = re.compile(r'挑战在(.{2,5})的(.{5,15})(\s?)')
        

        def _筋斗云CD自减():
            while True:
                for index in range(len(self.configs)):
                    if 局_筋斗云剩余CD[index] > 0:
                        局_筋斗云剩余CD[index] -= 1
                time.sleep(1)

        def _开始前任务():
            self.gto_地图房间('map.mopcity|皇宫大门')
            self.组队()
            self.全体执行命令('endTaoZhan')
        
        def _结束后任务():
            self.gto_地图房间('map.maoyin|装备店')

        def _队长领取任务():

            while True:
                self.myj_objects[0].send_orders('gettask one',0.5)
                局_res = 局_领取任务文本正则.findall(self.myj_objects[0].Npc对话框内容)
                if len(局_res) >= 1:
                    return
        
        def _全体提交任务():
            self.gto_地图房间('map.mopcity|皇宫大门')
            self.全体执行命令('endTaoZhan')

        def _选人释放筋斗云()->int:
            while True:
                for i in range(len(self.myj_objects)):
                    if 局_筋斗云剩余CD[i] <= 0 and 局_筋斗云原始CD[i] > 0:
                        while self.myj_objects[i].房间 == '皇宫大门':
                            self.myj_objects[i].send_orders('perform 搜索传送|perform 高级搜索传送')
                        局_筋斗云剩余CD[i] = 局_筋斗云原始CD[i]
                        return i            
                time.sleep(1)
        
        def _战斗():
            for 局_怪物 in self.myj_objects[0].Npc可攻击列表:
                if self.myj_objects[0].宠物名 in 局_怪物[1] or self.myj_objects[0].宠物名 in 局_怪物[3]:
                    self.myj_objects[0].send_orders(f'bar34 {局_怪物[0]} {局_怪物[2]}')
                    break
            局_标记 = True
            while 局_标记:
                for 局_怪物 in self.myj_objects[0].Npc可攻击列表:
                    if (self.myj_objects[0].宠物名 in 局_怪物[1] or self.myj_objects[0].宠物名 in 局_怪物[3]) and '尸体' in 局_怪物[3]:
                        局_标记 = False
                        break
                time.sleep(0.5)
                
        _开始前任务()
        threading.Thread(target=_筋斗云CD自减).start()
        局_计次 = 0
        while True:
            局_计次 += 1
            _队长领取任务()
            print(f'领取第{局_计次}环任务')
            局_索引 = _选人释放筋斗云()
            self.gto_序号目标(局_索引)
            _战斗()
            _全体提交任务()
            print(f'完成第{局_计次}环任务')
        _结束后任务()

def 哈刚():
    with open('哈刚账密.json', encoding='utf-8') as f:
        configs = json.loads(f.read())
    for config in configs:
        config['服务器'] = 'x12'
    team = MYJTeam(configs)
    team.gto_地图房间('map.mopcity|石之谜')
    while True:
        t = datetime.datetime.now()
        while True:
            team.全体执行命令('buyGang',0.28)
    input()


def 悬赏():
    configs = [
        {
            "服务器": "x12",
            "账号": "1425661289",
            "密码": "RgXyy1024.",
            "角色序号": "0",
            "刷怪配置": {
                "释放技能列表": [
                    "致命",
                    "出血[6级]",
                    "冥火爪[11级]",
                    "猛抽",
                    "强力打击[18级]",
                    "心灵尖啸",
                    "极寒冰霜[1级]",
                    "致命一击",
                    "水之潮汐[5级]",
                    "灵魂吸取"],
            },
            "筋斗云CD": 150
        },
        {
            "服务器": "x12",
            "账号": "dengjianabei",
            "密码": "000000",
            "角色序号": "0",    
            "筋斗云CD": 150
        },
        {
            "服务器": "x12",
            "账号": "guoguocangku",
            "密码": "yfydq0101",
            "角色序号": "0",    
            "筋斗云CD": 0
        },
    ]
    team = MYJTeam(configs)
    team.悬赏()
    input()

if __name__ == '__main__':
    # 哈刚()
    悬赏()